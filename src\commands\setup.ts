import {
  ActionRow<PERSON><PERSON>er,
  <PERSON>ton<PERSON><PERSON>er,
  ButtonStyle,
  <PERSON>rdAPIError,
  EmbedBuilder,
  PermissionFlagsBits,
  PermissionsBitField,
  SlashCommandBuilder,
  type ChatInputCommandInteraction,
  type Guild,
  type Client,
  type CategoryChannel,
  MessageFlags,
} from 'discord.js';
import dataManager from '../utils/dataManager';
import { DEFAULT_GUILD_SETTINGS } from '../data/models/guild';
import { EMOJI } from '../constants/emoji';

const ERROR_CODES = {
  UNKNOWN_CHANNEL: 10003,
  UNKNOWN_GUILD: 10004,
  UNKNOWN_MEMBER: 10007,
  MISSING_PERMISSIONS: 50013,
  UNKNOWN_MESSAGE: 10008,
  MAX_CHANNELS: 30005,
  CANNOT_EXECUTE_ON_DM: 50003,
  RATE_LIMITED: 20028,
  INVALID_WEBHOOK_TOKEN: 50027,
  MISSING_ACCESS: 50001,
};

/**
 * Checks the bot's role position in the guild hierarchy
 * @param {Guild} guild - The guild to check
 * @returns {Object} Object with warning flag and diagnostic information
 */

/**
 * Check if the bot has all the required permissions
 * @param {Guild} guild - The Discord guild to check permissions in
 * @returns {Object} An object with success flag and any missing permissions
 */
function checkBotPermissions(guild: Guild) {
  const requiredPermissions = [
    { flag: PermissionFlagsBits.ViewChannel, name: 'View Channels' },
    { flag: PermissionFlagsBits.ManageChannels, name: 'Manage Channels' },
    { flag: PermissionFlagsBits.Connect, name: 'Connect' },
    { flag: PermissionFlagsBits.SendMessages, name: 'Send Messages' },
    { flag: PermissionFlagsBits.ManageRoles, name: 'Manage Roles' },
    { flag: PermissionFlagsBits.EmbedLinks, name: 'Embed Links' },
  ];

  const botMember = guild.members.me;
  const missingPermissions: any[] = [];

  for (const perm of requiredPermissions) {
    if (!botMember.permissions.has(perm.flag)) {
      missingPermissions.push(perm.name);
    }
  }

  return {
    success: missingPermissions.length === 0,
    missing: missingPermissions,
  };
}

/**
 * Check if the bot has all required permissions in a specific category
 * @param {CategoryChannel} category - The category channel to check
 * @returns {Object} Object with success flag and missing permissions
 */
function checkCategoryPermissions(category: CategoryChannel) {
  const requiredPermissions = [
    { flag: PermissionFlagsBits.ViewChannel, name: 'View Channels' },
    { flag: PermissionFlagsBits.ManageChannels, name: 'Manage Channels' },
    { flag: PermissionFlagsBits.Connect, name: 'Connect' },
  ];

  const botMember = category.guild.members.me;
  const botPermsInCategory = botMember.permissionsIn(category);
  const missingPermissions = [];

  for (const perm of requiredPermissions) {
    if (!botPermsInCategory.has(perm.flag)) {
      missingPermissions.push(perm.name);
    }
  }

  return {
    success: missingPermissions.length === 0,
    missing: missingPermissions,
  };
}

/**
 * Centralized error handler for setup command
 * @param {string} source - The function or context where the error occurred
 * @param {Error} error - The error object
 * @param {Object} context - Additional context information
 */
function handleSetupError(source: string, error: any, client: Client, context: any = {}) {
  const timestamp = `[${new Date().toLocaleTimeString()}]`;

  if (error instanceof DiscordAPIError) {
    console.error(`${timestamp} [API ERROR] ${source}: Code ${error.code} - ${error.message}`);

    switch (error.code) {
      case ERROR_CODES.UNKNOWN_CHANNEL:
        console.error(
          `${timestamp} [API ERROR] The referenced channel does not exist or was deleted`
        );
        break;
      case ERROR_CODES.UNKNOWN_GUILD:
        console.error(
          `${timestamp} [API ERROR] The referenced guild does not exist or was deleted`
        );
        break;
      case ERROR_CODES.UNKNOWN_MEMBER:
        console.error(
          `${timestamp} [API ERROR] The referenced user is not a member of the specified guild`
        );
        break;
      case ERROR_CODES.MISSING_PERMISSIONS:
        console.error(`${timestamp} [API ERROR] The bot lacks permissions to perform this action`);
        if (context.interaction) {
          try {
            const errorMessage = `${EMOJI[client.user.id].CROSS} **${timestamp} Error**: Missing permissions to ${context.action || 'perform that action'}. Please check the bot's role permissions.`;

            if (context.interaction.deferred || context.interaction.replied) {
              context.interaction
                .fetchReply()
                .then(reply => {
                  const newContent = reply.content
                    ? `${reply.content}\n\n${errorMessage}`
                    : errorMessage;

                  context.interaction
                    .editReply({
                      content: newContent,
                    })
                    .catch(() => {});
                })
                .catch(() => {
                  context.interaction
                    .editReply({
                      content: errorMessage,
                    })
                    .catch(() => {});
                });
            } else {
              context.interaction
                .reply({
                  content: errorMessage,
                  flags: MessageFlags.Ephemeral,
                })
                .catch(() => {});
            }
          } catch {}
        }
        break;
      case ERROR_CODES.MISSING_ACCESS:
        console.error(`${timestamp} [API ERROR] The bot does not have access to the channel/guild`);
        break;
      case ERROR_CODES.MAX_CHANNELS:
        console.error(
          `${timestamp} [API ERROR] The server has reached the maximum number of channels (500)`
        );
        if (context.interaction) {
          try {
            const errorMessage = `${EMOJI[client.user.id].CROSS} **${timestamp} Error**: This server has reached the maximum number of channels allowed by Discord (500).`;

            if (context.interaction.deferred || context.interaction.replied) {
              context.interaction
                .fetchReply()
                .then(reply => {
                  const newContent = reply.content
                    ? `${reply.content}\n\n${errorMessage}`
                    : errorMessage;

                  context.interaction
                    .editReply({
                      content: newContent,
                    })
                    .catch(() => {});
                })
                .catch(() => {
                  context.interaction
                    .editReply({
                      content: errorMessage,
                    })
                    .catch(() => {});
                });
            } else {
              context.interaction
                .reply({
                  content: errorMessage,
                  flags: MessageFlags.Ephemeral,
                })
                .catch(() => {});
            }
          } catch {}
        }
        break;
      case ERROR_CODES.RATE_LIMITED:
        console.error(
          `${timestamp} [API ERROR] Rate limited by Discord API. Consider adding delays between operations.`
        );
        break;
      default:
        console.error(`${timestamp} [API ERROR] Discord API returned an error: ${error.message}`);
    }
  } else {
    console.error(`${timestamp} [ERROR] ${source}: ${error.message}`);
    if (error.stack) {
      console.error(`${timestamp} [ERROR STACK] ${error.stack.split('\n').slice(0, 3).join('\n')}`);
    }
  }
}

export const data = new SlashCommandBuilder()
  .setName('setup')
  .setDescription('Sets up the MyVC system (Admin or Manage Server permissions required)')
  .setDefaultMemberPermissions(PermissionFlagsBits.Administrator | PermissionFlagsBits.ManageGuild)
  .addBooleanOption(option =>
    option
      .setName('recreate')
      .setDescription('Delete existing channels and create new ones')
      .setRequired(false)
  );

export const execute = async (interaction: ChatInputCommandInteraction, client: Client) => {
  try {
    if (!interaction.guild || !interaction.memberPermissions) {
      return interaction.reply({
        content:
          'This command can only be used in a server where you have appropriate permissions.',
        flags: MessageFlags.Ephemeral,
      });
    }

    if (
      !interaction.memberPermissions.has(PermissionFlagsBits.Administrator) &&
      !interaction.memberPermissions.has(PermissionFlagsBits.ManageGuild)
    ) {
      const embed = new EmbedBuilder()
        .setColor(0xe74c3c)
        .setDescription(
          `${EMOJI[client.user.id].CROSS} You need Administrator or Manage Server permission to use this command.`
        );

      return interaction.reply({
        embeds: [embed],
        flags: MessageFlags.Ephemeral,
      });
    }

    const permissionCheck = checkBotPermissions(interaction.guild);
    if (!permissionCheck.success) {
      const missingPermsFormatted = permissionCheck.missing.map(p => `- ${p}`).join('\n');

      const embed = new EmbedBuilder()
        .setColor(0xe74c3c)
        .setTitle(`${EMOJI[client.user.id].CROSS} Missing Bot Permissions`)
        .setDescription(
          `The bot is missing the following required permissions to function properly:`
        )
        .addFields({
          name: 'Missing Permissions:',
          value: missingPermsFormatted,
        })
        .addFields({
          name: 'How to Fix:',
          value:
            'Please give the bot these permissions in Server Settings > Roles > [Bot Role] > Permissions',
        });

      return interaction.reply({
        embeds: [embed],
        flags: MessageFlags.Ephemeral,
      });
    }

    const timestamp = `\`${new Date().toLocaleString()}\``;
    const warnings = [];

    const permissionsCheck = checkBotPermissions(interaction.guild);
    if (permissionsCheck.missing && permissionsCheck.missing.length > 0) {
      warnings.push(
        `${timestamp} **Permission Warning**: Missing permissions: ${permissionsCheck.missing.join(', ')}`
      );
    }

    let category = null;
    let categoryId = null;

    const allCategories = interaction.guild.channels.cache.filter(
      c => c.name === 'MyVC Category' && c.type === 4
    );

    console.log(`[INFO] Found ${allCategories.size} MyVC categories`);

    if (allCategories.size > 0) {
      category = allCategories.first();
      categoryId = category?.id;
    }

    if (categoryId) {
      const categoryPermsCheck = checkCategoryPermissions(category);
      if (categoryPermsCheck && !categoryPermsCheck.success) {
        warnings.push(
          `${timestamp} **Category Permission Warning**: Missing ${categoryPermsCheck.missing.join(', ')} in the MyVC category.`
        );
      }
    }

    const recreate = interaction?.options?.getBoolean('recreate') || false;

    const statusEmbed = new EmbedBuilder()
      .setColor(0x3498db)
      .setTitle('⚙️ MyVC Setup In Progress')
      .setDescription(`${recreate ? '🔄 Recreating' : '🔍 Checking for existing'} channels...`)
      .addFields({
        name: 'Status',
        value: '⏳ Please wait while we set up your server...',
      });

    await interaction.reply({
      embeds: [statusEmbed],
      flags: MessageFlags.Ephemeral,
    });

    const guild = interaction.guild;
    const defaultLimit = 0;

    let interfaceChannel = null;
    let JOIN_TO_CREATE = null;

    await guild.channels.fetch().catch(error => {
      handleSetupError('FetchGuildChannels', error, client, {
        interaction,
        action: 'fetch channels',
      });
    });

    if (recreate) {
      console.log('[INFO] Recreate flag is true, deleting all existing MyVC categories...');

      const currentGuildSettings = dataManager.loadGuildSettings();
      const guildConfig = currentGuildSettings[guild.id] || {};

      for (const [_, existingCategory] of allCategories) {
        try {
          console.log(`[INFO] Deleting category ${existingCategory.id} and all its channels`);

          const categoryChannels = guild.channels.cache.filter(
            c => c.parentId === existingCategory.id
          );
          for (const [__, channel] of categoryChannels) {
            await channel.delete().catch(error => {
              if (error.code === ERROR_CODES.UNKNOWN_CHANNEL) {
                console.log(`[INFO] Channel ${channel.name} already deleted`);
              } else if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
                console.error(`[ERROR] Missing permissions to delete channel ${channel.name}`);
              } else {
                handleSetupError('DeleteChannel', error, client, { channelName: channel.name });
              }
            });
            console.log(`[INFO] Deleted channel ${channel.name} (${channel.id})`);

            if (guildConfig.joinChannelId === channel.id) {
              console.log(
                `[INFO] Deleted join channel ${channel.id}, cleaning up associated temp channels`
              );

              const tempChannelsData = dataManager.loadTempChannels();
              let changed = false;

              for (const channelId of Object.keys(tempChannelsData)) {
                const ch = guild.channels.cache.get(channelId);
                if (!ch || ch.guildId === guild.id) {
                  delete tempChannelsData[channelId];
                  changed = true;
                  console.log(`[INFO] Removed temp channel record for ${channelId}`);
                }
              }

              if (changed) {
                dataManager.saveTempChannels(tempChannelsData);
                console.log('[INFO] Saved updated temp channels data after cleanup');
              }
            }
          }

          await existingCategory.delete().catch(error => {
            if (error.code === ERROR_CODES.UNKNOWN_CHANNEL) {
              console.log('[INFO] Category already deleted');
            } else if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
              console.error('[ERROR] Missing permissions to delete category');
            } else {
              handleSetupError('DeleteCategory', error, client);
            }
          });
          console.log(`[INFO] Deleted MyVC Category ${existingCategory.id}`);

          if (guildConfig.tempCategoryId === existingCategory.id) {
            delete guildConfig.tempCategoryId;
            delete guildConfig.joinChannelId;
            currentGuildSettings[guild.id] = guildConfig;
            dataManager.saveGuildSettings(currentGuildSettings);
            console.log('[INFO] Removed deleted category and join channel from guild settings');
          }
        } catch (error) {
          handleSetupError('DeleteCategoryAndChannels', error, client);
        }
      }

      category = null;
      categoryId = null;
      interfaceChannel = null;
      JOIN_TO_CREATE = null;

      await guild.channels.fetch().catch(error => {
        handleSetupError('RefetchAfterDeletion', error, client);
      });
    } else {
    }

    if (category) {
      interfaceChannel = guild.channels.cache.find(
        c => c.name === 'controller' && c.type === 0 && c.parentId === category?.id
      );

      JOIN_TO_CREATE = guild.channels.cache.find(
        c => c.name === 'JOIN_TO_CREATE' && c.type === 2 && c.parentId === category?.id
      );
    }

    async function createAllChannels(): Promise<boolean> {
      if (!category) {
        console.log('[INFO] Creating new MyVC Category...');

        try {
          try {
            category = await guild.channels.create({
              name: 'MyVC Category',
              type: 4,
              permissionOverwrites: [
                {
                  id: guild.id,
                  allow: [PermissionsBitField.Flags.Connect],
                  deny: [],
                },
              ],
            });
            console.log(`[INFO] Created new MyVC Category (ID: ${category.id})`);

            await category.permissionOverwrites.edit(guild.members.me.id, {
              allow: [
                PermissionsBitField.Flags.ViewChannel,
                PermissionsBitField.Flags.ManageChannels,
                PermissionsBitField.Flags.ManageRoles,
                PermissionsBitField.Flags.Connect,
                PermissionsBitField.Flags.SendMessages,
                PermissionsBitField.Flags.EmbedLinks,
              ],
              deny: [],
            });

            const categoryPermCheck = checkCategoryPermissions(category);
            if (!categoryPermCheck.success) {
              const missingPermsFormatted = categoryPermCheck.missing.join(', ');
              console.warn(
                `[WARN] Bot is missing permissions in the category: ${missingPermsFormatted}`
              );

              const categoryTimestamp = `\`[${new Date().toLocaleTimeString()}]\``;
              warnings.push(
                `${categoryTimestamp} **Category Permission Warning**: Missing ${missingPermsFormatted} in the MyVC category.`
              );
            }
          } catch (error) {
            if (error.code === ERROR_CODES.MAX_CHANNELS) {
              console.error('[ERROR] Server has reached maximum channel limit (500)');
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create channels: This server has reached Discord's 500 channel limit.`,
                })
                .catch(() => {});
              return false;
            }
            if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
              console.error('[ERROR] Missing permissions to create category');
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create category: Missing permissions. Please check the bot's role permissions.`,
                })
                .catch(() => {});
              return false;
            }
            handleSetupError('CreateCategory', error, client, { interaction });
            return false;
          }

          const categoryCreatedEmbed = new EmbedBuilder()
            .setColor(0x3498db)
            .setTitle('⚙️ MyVC Setup In Progress')
            .setDescription('Setting up channels...')
            .addFields({
              name: 'Status',
              value: [
                `${EMOJI[client.user.id].CHECK} Category created successfully`,
                '⏳ Creating controller channel...',
              ].join('\n'),
            });

          await interaction
            .editReply({
              embeds: [categoryCreatedEmbed],
            })
            .catch(() => {});

          await new Promise(r => setTimeout(r, 1000));

          try {
            interfaceChannel = await guild.channels.create({
              name: 'controller',
              type: 0,
              parent: category.id,
              permissionOverwrites: [
                {
                  id: guild.id,
                  allow: [PermissionsBitField.Flags.Connect],
                  deny: [
                    PermissionsBitField.Flags.SendMessages,
                    PermissionsBitField.Flags.AddReactions,
                  ],
                },
              ],
            });
            console.log(`[INFO] Created controller channel (ID: ${interfaceChannel.id})`);

            await interfaceChannel.permissionOverwrites.edit(guild.members.me.id, {
              allow: [
                PermissionsBitField.Flags.ViewChannel,
                PermissionsBitField.Flags.ManageChannels,
                PermissionsBitField.Flags.ManageRoles,
                PermissionsBitField.Flags.Connect,
                PermissionsBitField.Flags.SendMessages,
                PermissionsBitField.Flags.EmbedLinks,
              ],
              deny: [],
            });
          } catch (error) {
            if (error.code === ERROR_CODES.MAX_CHANNELS) {
              console.error('[ERROR] Server has reached maximum channel limit (500)');
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create controller channel: This server has reached Discord's 500 channel limit.`,
                })
                .catch(() => {});

              await category.delete().catch(() => {});
              return false;
            }
            if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
              console.error('[ERROR] Missing permissions to create controller channel');
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create controller channel: Missing permissions. Please check the bot's role permissions.`,
                })
                .catch(() => {});

              await category.delete().catch(() => {});
              return false;
            }
            handleSetupError('CreatecontrollerChannel', error, client, { interaction });

            await category.delete().catch(() => {});
            return false;
          }

          const interfaceCreatedEmbed = new EmbedBuilder()
            .setColor(0x3498db)
            .setTitle('⚙️ MyVC Setup In Progress')
            .setDescription('Setting up channels...')
            .addFields({
              name: 'Status',
              value: [
                `${EMOJI[client.user.id].CHECK} Category created successfully`,
                `${EMOJI[client.user.id].CHECK} controller channel created successfully`,
                '⏳ Creating voice channel...',
              ].join('\n'),
            });

          await interaction
            .editReply({
              embeds: [interfaceCreatedEmbed],
            })
            .catch(() => {});

          await new Promise(r => setTimeout(r, 1000));

          try {
            JOIN_TO_CREATE = await guild.channels.create({
              name: 'JOIN_TO_CREATE',
              type: 2,
              parent: category.id,
              userLimit: defaultLimit,
              permissionOverwrites: [
                {
                  id: guild.id,
                  allow: [PermissionsBitField.Flags.Connect],
                  deny: [],
                },
              ],
            });
            console.log(
              `[INFO] Created JOIN_TO_CREATE with limit: unlimited (ID: ${JOIN_TO_CREATE.id})`
            );

            await JOIN_TO_CREATE.permissionOverwrites.edit(guild.members.me.id, {
              allow: [
                PermissionsBitField.Flags.ViewChannel,
                PermissionsBitField.Flags.ManageChannels,
                PermissionsBitField.Flags.ManageRoles,
                PermissionsBitField.Flags.Connect,
                PermissionsBitField.Flags.SendMessages,
                PermissionsBitField.Flags.EmbedLinks,
              ],
              deny: [],
            });
          } catch (error) {
            if (error.code === ERROR_CODES.MAX_CHANNELS) {
              console.error(`[ERROR] Server has reached maximum channel limit (500)`);
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create JOIN_TO_CREATE: This server has reached Discord's 500 channel limit.`,
                })
                .catch(() => {});

              await interfaceChannel.delete().catch(() => {});
              await category.delete().catch(() => {});
              return false;
            }
            if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
              console.error(`[ERROR] Missing permissions to create JOIN_TO_CREATE`);
              await interaction
                .editReply({
                  content: `${EMOJI[client.user.id].CROSS} Cannot create JOIN_TO_CREATE: Missing permissions. Please check the bot's role permissions.`,
                })
                .catch(() => {});

              await interfaceChannel.delete().catch(() => {});
              await category.delete().catch(() => {});
              return false;
            }
            handleSetupError('CreateJOIN_TO_CREATE', error, client, { interaction });

            await interfaceChannel.delete().catch(() => {});
            await category.delete().catch(() => {});
            return false;
          }

          return true;
        } catch (error) {
          handleSetupError('CreateAllChannels', error, client, { interaction });
          return false;
        }
      }
      return true;
    }

    const channelsCreated = await createAllChannels();

    if (!channelsCreated) {
      console.error('[ERROR] Failed to create MyVC channels');
      await interaction
        .editReply({
          content: `${EMOJI[client.user.id].CROSS} Failed to create MyVC channels. Please check the bot's permissions and try again.`,
        })
        .catch(() => {});
      return;
    }

    if (!interfaceChannel || !JOIN_TO_CREATE) {
      console.error('[ERROR] Failed to find or create channels');
      await interaction
        .editReply({
          content: `${EMOJI[client.user.id].CROSS} Failed to find or create necessary channels. Please try again or check the server for existing channels.`,
        })
        .catch(() => {});
      return;
    }

    const dbResult = await updateGuildSettings(
      client,
      guild.id,
      JOIN_TO_CREATE.id,
      interfaceChannel.id,
      category.id
    );
    if (!dbResult.success) {
      console.error(
        `[ERROR] Failed to update guild settings: ${dbResult.error || 'Unknown error'}`
      );
    }

    console.log('[INFO] Refreshing controller channel...');

    try {
      const existingMessages = await interfaceChannel.messages.fetch({ limit: 20 }).catch(error => {
        if (error.code === ERROR_CODES.MISSING_ACCESS) {
          console.error(`[ERROR] Cannot access messages in controller channel`);
          return { size: 0 };
        }
        handleSetupError('FetchMessages', error, client);
        return { size: 0 };
      });

      const botInterfaceMessages = existingMessages.filter(
        msg =>
          msg.author.id === client.user.id &&
          msg.embeds.length > 0 &&
          msg.embeds[0].title &&
          (msg.embeds[0].title.includes('MyVC') || msg.embeds[0].title.includes('Voice Controls'))
      );

      console.log(
        `[INFO] Found ${botInterfaceMessages.size} existing controller messages to clean up`
      );

      for (const [, message] of botInterfaceMessages) {
        await message.delete().catch(error => {
          if (error.code === ERROR_CODES.UNKNOWN_MESSAGE) {
            console.log(`[INFO] Message ${message.id} already deleted`);
          } else if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
            console.error(`[ERROR] Missing permissions to delete message ${message.id}`);
          } else {
            handleSetupError('DeletecontrollerMessage', error, client);
          }
        });
        console.log(`[INFO] Deleted controller message: ${message.id}`);
      }

      if (botInterfaceMessages.size === 0) {
        console.log('[INFO] No specific controller messages found, performing general cleanup');

        let messagesDeleted = false;
        let lastMessageId = null;

        while (true) {
          const options: any = { limit: 100 };
          if (lastMessageId) options.before = lastMessageId;

          const messages = await interfaceChannel.messages.fetch(options).catch(error => {
            if (error.code === ERROR_CODES.MISSING_ACCESS) {
              console.error(`[ERROR] Cannot access messages in controller channel`);
              return { size: 0 };
            }
            handleSetupError('FetchMessages', error, client);
            return { size: 0 };
          });

          if (messages.size === 0) break;

          const botMessages = messages.filter(msg => msg.author.id === client.user.id);
          if (botMessages.size === 0) {
            lastMessageId = messages.last().id;
            continue;
          }

          const recentMessages = botMessages.filter(msg => {
            return Date.now() - msg.createdTimestamp < 1209600000;
          });

          if (recentMessages.size > 0) {
            await interfaceChannel.bulkDelete(recentMessages).catch(error => {
              if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
                console.error(`[ERROR] Missing permissions to bulk delete messages`);
              } else {
                handleSetupError('BulkDeleteMessages', error, client);
              }
            });
            messagesDeleted = true;
          }

          const oldMessages = botMessages.filter(msg => {
            return Date.now() - msg.createdTimestamp >= 1209600000;
          });

          for (const [, message] of oldMessages) {
            await message.delete().catch(error => {
              if (error.code === ERROR_CODES.UNKNOWN_MESSAGE) {
              } else if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
                console.error(`[ERROR] Missing permissions to delete message`);
              } else {
                handleSetupError('DeleteMessage', error, client);
              }
            });
            messagesDeleted = true;
          }

          if (messages.size < 100) break;

          lastMessageId = messages.last().id;
        }

        console.log(
          `[INFO] Completed general message cleanup (messagesDeleted: ${messagesDeleted})`
        );
      }
    } catch (error) {
      handleSetupError('ClearMessages', error, client, { interaction });

      console.log('[INFO] Continuing with setup despite message cleanup errors');
    }

    const row1 = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('name')
        .setEmoji(EMOJI[client.user.id].NAME)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('limit')
        .setEmoji(EMOJI[client.user.id].LIMIT)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('privacy')
        .setEmoji(EMOJI[client.user.id].PRIVACY)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('waiting')
        .setEmoji(EMOJI[client.user.id].WAITING_ROOM)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('region')
        .setEmoji(EMOJI[client.user.id].REGION)
        .setStyle(ButtonStyle.Secondary)
    );

    const row2 = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('trust')
        .setEmoji(EMOJI[client.user.id].TRUST)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('untrust')
        .setEmoji(EMOJI[client.user.id].UNTRUST)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('invite')
        .setEmoji(EMOJI[client.user.id].INVITE)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('kick')
        .setEmoji(EMOJI[client.user.id].KICK)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('claim')
        .setEmoji(EMOJI[client.user.id].CLAIM)
        .setStyle(ButtonStyle.Secondary)
    );

    const row3 = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('block')
        .setEmoji(EMOJI[client.user.id].BLOCK)
        .setStyle(ButtonStyle.Secondary),

      new ButtonBuilder()
        .setCustomId('unblock')
        .setEmoji(EMOJI[client.user.id].UNBLOCK)
        .setStyle(ButtonStyle.Secondary),

      new ButtonBuilder()
        .setCustomId('transfer')
        .setEmoji(EMOJI[client.user.id].OWNERSHIP)
        .setStyle(ButtonStyle.Secondary),

      new ButtonBuilder()
        .setCustomId('delete')
        .setEmoji(EMOJI[client.user.id].DELETE)
        .setStyle(ButtonStyle.Secondary)
    );

    console.log('[INFO] Sending controller messages...');

    try {
      const message = `
Join the <#${JOIN_TO_CREATE.id}> channel to create your own voice channel.

**Button Controls:**

**Channel Settings**
> ${EMOJI[client.user.id].NAME} \`Title\` ・ ${EMOJI[client.user.id].LIMIT} \`User Limit\` ・ ${EMOJI[client.user.id].PRIVACY} \`Privacy\` ・ ${EMOJI[client.user.id].WAITING_ROOM} \`Waiting Room\`
・${EMOJI[client.user.id].REGION} \`Region\`

**User Controls**
> ${EMOJI[client.user.id].TRUST} \`Trust\` ・ ${EMOJI[client.user.id].UNTRUST} \`Untrust\` ・ ${EMOJI[client.user.id].INVITE} \`Invite\` ・ ${EMOJI[client.user.id].KICK} \`Kick\` ・${EMOJI[client.user.id].CLAIM} \`Claim\`

**Advanced**
> ${EMOJI[client.user.id].BLOCK} \`Block\` ・ ${EMOJI[client.user.id].UNBLOCK} \`Unblock\` ・ ${EMOJI[client.user.id].OWNERSHIP} \`Transfer\` ・ ${EMOJI[client.user.id].DELETE} \`Delete\`

Your settings are saved globally across all servers.
`;

      const interfaceEmbed2 = new EmbedBuilder()
        .setColor(0x5865f2)
        .setTitle('MyVC Controls')
        .setDescription(message);

      const interfaceMsg = await interfaceChannel
        .send({
          embeds: [interfaceEmbed2],
          components: [row1, row2, row3],
        })
        .catch(error => {
          if (error.code === ERROR_CODES.MISSING_PERMISSIONS) {
            console.error(`[ERROR] Missing permissions to send messages to controller channel`);
            throw error;
          }
          handleSetupError('SendcontrollerMessage', error, client);
          throw error;
        });

      console.log(
        `[INFO] Sent controller message with embed and buttons to controller channel (ID: ${interfaceMsg.id})`
      );

      let databaseStatus = `${EMOJI[client.user.id].CHECK} Settings saved to database`;

      if (!dbResult.success) {
        databaseStatus = '⚠️ Settings temporarily stored in memory (database update failed)';
        warnings.push(
          `${timestamp} **Database Warning**: Failed to save settings to database - ${dbResult.error || 'Unknown error'}. Settings are temporarily stored in memory.`
        );
      } else if (dbResult.fromCache) {
        databaseStatus =
          '⚠️ Settings temporarily stored in memory (will sync to database when connection is available)';
        warnings.push(
          `${timestamp} **Database Warning**: MongoDB connection not available. Settings are temporarily stored in memory and will sync when connection is restored.`
        );
      }

      const setupCompleteEmbed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle(`${EMOJI[client.user.id].CHECK} MyVC Setup Complete`)
        .setDescription('The temporary voice channel system has been successfully set up!')
        .addFields(
          {
            name: '📋 Setup Summary',
            value: [
              `**Category**: ${category.name} (${category.id})`,
              `**Voice Channel**: ${JOIN_TO_CREATE.name} (${JOIN_TO_CREATE.id})`,
              `**Interface Channel**: ${interfaceChannel.name} (${interfaceChannel.id})`,
            ].join('\n'),
          },
          {
            name: '🎙️ How to Use',
            value:
              'Users can join the "JOIN_TO_CREATE" voice channel to create their own temporary voice channel. The controller channel provides instructions and information.',
          }
        )
        .setFooter({
          text: 'MyVC Bot • Setup completed successfully',
          iconURL: client.user.displayAvatarURL(),
        })
        .setTimestamp();

      if (warnings.length > 0) {
        setupCompleteEmbed.addFields({
          name: '⚠️ Warnings',
          value: warnings.join('\n') || 'None',
        });
      }

      setupCompleteEmbed.addFields({
        name: '📁 Database Status',
        value: databaseStatus,
      });

      await interaction.editReply({
        embeds: [setupCompleteEmbed],
      });

      console.log(
        `[INFO] Setup completed for guild ${guild.name} with Join Channel ID: ${JOIN_TO_CREATE.id} and Category ID: ${category.id}`
      );
    } catch (error) {
      handleSetupError('SendcontrollerMessages', error, client, { interaction });

      try {
        await interaction
          .editReply({
            content: `${EMOJI[client.user.id].CROSS} Setup completed partially but there was an issue with the controller messages. Please check the bot's permissions and try again.`,
          })
          .catch(() => {});
      } catch {}

      const settings = await dataManager.getGuildSettings(guild.id, true);
      console.log(
        `[INFO] Verified cached settings: JoinChannel=${settings.joinChannelId}, TempCategory=${settings.tempCategoryId}`
      );
    }
  } catch (error) {
    console.log(error);
    handleSetupError('SetupCommand', error, client, { interaction });

    try {
      const timestamp = `\`[${new Date().toLocaleTimeString()}]\``;
      const errorMessage = `${EMOJI[client.user.id].CROSS} **${timestamp} Error**: There was an error during setup. Please check the bot's permissions and try again.`;

      if (interaction.deferred || interaction.replied) {
        interaction
          .fetchReply()
          .then(reply => {
            const newContent = reply.content ? `${reply.content}\n\n${errorMessage}` : errorMessage;

            interaction
              .editReply({
                content: newContent,
              })
              .catch(() => {});
          })
          .catch(() => {
            interaction
              .editReply({
                content: errorMessage,
              })
              .catch(() => {});
          });
      } else {
        await interaction
          .reply({
            content: errorMessage,
            flags: MessageFlags.Ephemeral,
          })
          .catch(() => {});
      }
    } catch {}
  }
};

/**
 * Update the guild settings with channel IDs
 * @param {Client} client - Discord.js client
 * @param {string} guildId - ID of the guild
 * @param {string} joinChannelId - ID of the JOIN_TO_CREATE channel
 * @param {string} categoryId - ID of the category
 */
async function updateGuildSettings(
  client: Client,
  guildId: string,
  joinChannelId: string,
  interfaceChannel: string,
  categoryId: string
): Promise<{ success: boolean; fromCache?: boolean; error?: string }> {
  try {
    const success = await dataManager.forceSyncGuildSettings(
      guildId,
      joinChannelId,
      interfaceChannel,
      categoryId
    );

    if (!success) {
      throw new Error('Database update returned false');
    }

    const settings = {
      ...DEFAULT_GUILD_SETTINGS,
      guildId,
      joinChannelId,
      interfaceChannel,
      tempCategoryId: categoryId,
      lastUpdated: new Date(),
    };

    const cacheKey = `guild_${guildId}`;
    dataManager.setCache(cacheKey, settings);
    dataManager.emit('guildSettingsChanged', guildId, settings);

    console.log(
      `[INFO] Cache updated for guild ${guildId} with new values: JoinChannel=${settings.joinChannelId}, TempCategory=${settings.tempCategoryId}`
    );

    console.log(`[INFO] Successfully updated guild settings for ${guildId}`);
    return { success: true };
  } catch (error) {
    console.error(`[ERROR] Failed to update guild settings: ${error.message}`);
    return { success: false, error: error.message };
  }
}
