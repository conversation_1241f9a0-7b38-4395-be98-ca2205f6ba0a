import {
  Interaction,
  MessageFlags,
  VoiceChannel,
  PermissionFlagsBits,
  EmbedBuilder,
} from 'discord.js';
import logger from '../utils/logger';

import { getUserVoiceChannel } from '../utils/channelUtils';
import {
  handleInteractionError,
  deferReplyEphemeral,
  replyOrFollowUpEphemeral,
} from '../utils/interactionUtils';

import * as blockHandler from './block';
import * as claimHandler from './claim';
import * as deleteHandler from './delete';
import * as inviteHandler from './invite';
import * as kickHandler from './kick';
import * as limitHandler from './limit';
import * as nameHandler from './name';
import * as privacyHandler from './privacy';
import * as regionHandler from './region';
import * as transferHandler from './transfer';
import * as trustHandler from './trust';
import * as unblockHandler from './unblock';
import * as untrustHandler from './untrust';
import * as waitingHandler from './waiting';
import * as channelWelcomeHandler from './channelWelcome';
import { EMOJI } from '../constants/emoji';
import { tempChannelRepository } from '../data/repositories/tempChannel';
import { execute as setupExecute } from '../commands/setup';
import { COOLDOWN_TIERS } from '../types';

/**
 * Comprehensive cooldown management system
 *
 * Discord rate limits:
 * - Global rate limit: 50 requests per second
 * - Per-route limits (e.g., channel creation, message sending)
 * - Per-resource limits (e.g., guild, channel)
 *
 * We implement tiered cooldowns:
 * - TIER 1 (Light): Simple actions with minimal API impact (3s)
 * - TIER 2 (Moderate): Actions that use multiple API calls (5s)
 * - TIER 3 (Heavy): Resource-intensive actions (10s)
 * - TIER 4 (Critical): Actions that should be limited severely (15s)
 */

const cooldowns = new Map();

const ACTIONS_COOLDOWN = {
  name: COOLDOWN_TIERS.LIGHT,
  limit: COOLDOWN_TIERS.LIGHT,
  ping: COOLDOWN_TIERS.LIGHT,

  privacy: COOLDOWN_TIERS.MODERATE,
  region: COOLDOWN_TIERS.MODERATE,
  trust: COOLDOWN_TIERS.MODERATE,
  untrust: COOLDOWN_TIERS.MODERATE,
  kick: COOLDOWN_TIERS.MODERATE,
  invite: COOLDOWN_TIERS.MODERATE,
  waiting: COOLDOWN_TIERS.MODERATE,

  block: COOLDOWN_TIERS.HEAVY,
  unblock: COOLDOWN_TIERS.HEAVY,
  claim: COOLDOWN_TIERS.HEAVY,
  transfer: COOLDOWN_TIERS.HEAVY,

  delete: COOLDOWN_TIERS.CRITICAL,

  default: COOLDOWN_TIERS.LIGHT,
};

const GLOBAL_LIMIT = 45;
const globalRequests = {
  timestamps: [],
  count: 0,
};

/**
 * Check global rate limit
 * @returns {boolean} True if under limit, false if rate limited
 */
function checkGlobalLimit() {
  const now = Date.now();

  globalRequests.timestamps = globalRequests.timestamps.filter(time => now - time < 1000);

  if (globalRequests.timestamps.length >= GLOBAL_LIMIT) {
    return false;
  }

  globalRequests.timestamps.push(now);
  globalRequests.count++;

  if (globalRequests.count % 1000 === 0) {
    logger.info(`Global API usage: ${globalRequests.count} requests made so far`);
  }

  return true;
}

/**
 * Check cooldown for a specific user and action
 * @param {string} userId - User ID
 * @param {string} action - Action name
 * @param {string} [channelId] - Optional channel ID for per-channel limits
 * @returns {number} Seconds remaining on cooldown, or 0 if not on cooldown
 */
function checkCooldown(userId, action, channelId = null) {
  if (!checkGlobalLimit()) {
    return 1;
  }

  const now = Date.now();
  const userCooldowns = cooldowns.get(userId) || {};

  const cooldownKey = channelId ? `${action}-${channelId}` : action;

  const cooldownAmount = (ACTIONS_COOLDOWN[action] || ACTIONS_COOLDOWN.default) * 1000;

  if (userCooldowns[cooldownKey] && now < userCooldowns[cooldownKey] + cooldownAmount) {
    const timeLeft = (userCooldowns[cooldownKey] + cooldownAmount - now) / 1000;
    return Math.round(timeLeft);
  }

  userCooldowns[cooldownKey] = now;
  cooldowns.set(userId, userCooldowns);
  return 0;
}

/**
 * Clean up old cooldowns periodically to prevent memory leaks
 */
setInterval(() => {
  const now = Date.now();

  for (const [userId, userCooldowns] of cooldowns.entries()) {
    const expiredActions = [];
    for (const [action, timestamp] of Object.entries(userCooldowns) as any) {
      if (now > timestamp + COOLDOWN_TIERS.CRITICAL * 1000) {
        expiredActions.push(action);
      }
    }

    for (const action of expiredActions) {
      delete userCooldowns[action];
    }

    if (Object.keys(userCooldowns).length === 0) {
      cooldowns.delete(userId);
    }
  }

  logger.debug(`Cooldown cleanup: ${cooldowns.size} users with active cooldowns`);
}, 60000);

const buttonHandlers = {
  block: blockHandler,
  claim: claimHandler,
  delete: deleteHandler,
  invite: inviteHandler,
  kick: kickHandler,
  limit: limitHandler,
  name: nameHandler,
  privacy: privacyHandler,
  region: regionHandler,
  transfer: transferHandler,
  trust: trustHandler,
  unblock: unblockHandler,
  untrust: untrustHandler,
  waiting: waitingHandler,
  purge: channelWelcomeHandler,
};

const selectMenuHandlers = {
  block: blockHandler,
  unblock: unblockHandler,
  trust: trustHandler,
  untrust: untrustHandler,
  kick: kickHandler,
  invite: inviteHandler,
  privacy: privacyHandler,
  region: regionHandler,
  transfer: transferHandler,
  waiting: waitingHandler,
};

const modalSubmitHandlers = {
  name: nameHandler,
  limit: limitHandler,
  purge: channelWelcomeHandler,
};

export const name = 'interactionCreate';
export const execute = async (interaction, client) => {
  const source = `interaction:${interaction.id}:${interaction.type}`;

  try {
    if (!checkGlobalLimit()) {
      logger.warn(`Global rate limit triggered by interaction ${interaction.id}`);
      return;
    }

    logger.debug(`Received interaction: ${interaction.type} from ${interaction.user.tag}`);

    if (interaction.isChatInputCommand()) {
      const command = client.commands.get(interaction.commandName);
      if (!command) {
        logger.error(`No command matching ${interaction.commandName} was found.`);
        await replyOrFollowUpEphemeral(interaction, {
          content: `No command matching \`${interaction.commandName}\` was found.`,
        });
        return;
      }

      const userId = interaction.user.id;
      const commandName = interaction.commandName;
      const cooldownRemaining = checkCooldown(userId, commandName);
      if (cooldownRemaining > 0) {
        logger.debug(
          `User ${interaction.user.tag} on cooldown for command ${commandName} (${cooldownRemaining}s)`
        );
        await replyOrFollowUpEphemeral(interaction, {
          content: `⏳ Please wait ${cooldownRemaining} second(s) before using this command again.`,
        });
        return;
      }
      try {
        await command.execute(interaction, client);

        if (global.incrementCommandCount) {
          global.incrementCommandCount();
        }
      } catch (error) {
        await handleInteractionError(
          `${source}:command:${commandName}`,
          error,
          interaction,
          client
        );
      }
      return;
    } else if (interaction.isButton()) {
      const buttonId = interaction.customId;
      const buttonSource = `${source}:button:${buttonId}`;
      logger.debug(`Button clicked: ${buttonId} by ${interaction.user.tag}`);

      if (buttonId.startsWith('start_setup')) {
        if (
          !interaction.memberPermissions.has(PermissionFlagsBits.Administrator) &&
          !interaction.memberPermissions.has(PermissionFlagsBits.ManageGuild)
        ) {
          const embed = new EmbedBuilder()
            .setColor(0xe74c3c)
            .setDescription(
              `${EMOJI[client.user.id].CROSS} You need Administrator or Manage Server permission to use this setup button.`
            );

          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral,
          });
          return;
        }

        setupExecute(interaction, client);
        return;
      }

      if (buttonId.startsWith('transfer_confirm_')) {
        const targetUserId = buttonId.split('_')[3];
        const channelId = buttonId.split('_')[2];

        const userId = interaction.user.id;
        const newOwner = await interaction.guild.members.fetch(targetUserId).catch(() => null);
        if (!newOwner) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} The selected user is no longer in the server.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        if (newOwner.voice.channelId !== channelId) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} The selected user must be in the voice channel to receive ownership.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        client.tempChannels.set(channelId, targetUserId);

        const channel = await interaction.guild.channels.fetch(channelId).catch(() => null);
        if (!channel) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} Channel no longer exists.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        try {
          const botMember = interaction.guild.members.me;
          const botPermissions = channel.permissionsFor(botMember);

          if (!botPermissions.has('ManageRoles') || !botPermissions.has('ManageChannels')) {
            logger.warn(
              `Bot lacks required permissions to update channel permissions in ${channel.name} (${channelId})`
            );
            await interaction.editReply({
              content: `${EMOJI[client.user.id].UNTRUST} Channel ownership has been transferred to ${newOwner.user.tag}, but I couldn't update channel permissions due to missing bot permissions. The new owner may need to ask an admin to adjust permissions.`,
              flags: MessageFlags.SuppressEmbeds,
            });
            return;
          }

          try {
            await tempChannelRepository.setNewOwnerOfTempChanel(channelId, targetUserId);

            await channel.permissionOverwrites.create(
              targetUserId,
              {
                ViewChannel: true,
                Connect: true,
                Speak: true,
              },
              { reason: `Channel ownership transferred from ${interaction.user.tag}` }
            );

            logger.debug(
              `Successfully updated permissions for new owner ${newOwner.user.tag} in channel ${channel.name}`
            );
          } catch (permError) {
            logger.error(`Failed to update permissions for new owner: ${permError.message}`);
          }

          if (userId !== targetUserId) {
            try {
              await channel.permissionOverwrites.edit(userId, {
                MuteMembers: null,
                DeafenMembers: null,
                MoveMembers: null,
              });

              logger.debug(
                `Successfully updated permissions for previous owner ${interaction.user.tag} in channel ${channel.name}`
              );
            } catch (permError) {
              logger.error(`Failed to update permissions for previous owner: ${permError.message}`);
            }
          }

          const currentOwnerSettings = client.userSettings.get(userId);
          const newOwnerSettings = client.userSettings.get(targetUserId);

          interaction.deferUpdate().then(() =>
            interaction.editReply({
              content: `${EMOJI[client.user.id].CHECK} Channel ownership has been transferred to ${newOwner.user.tag}. Checking for user configuration...`,
              flags: MessageFlags.SuppressEmbeds,
            })
          );

          setTimeout(async () => {
            try {
              const updatedChannel = await interaction.guild.channels
                .fetch(channelId)
                .catch(() => null);
              if (!updatedChannel) {
                logger.warn(`Channel ${channelId} no longer exists after ownership transfer`);
                return;
              }

              const refreshedNewOwnerSettings = client.userSettings.get(targetUserId);

              const updatedBotPermissions = updatedChannel.permissionsFor(
                interaction.guild.members.me
              );
              const canManageChannel = updatedBotPermissions.has('ManageChannels');

              if (refreshedNewOwnerSettings) {
                logger.info(
                  `Applying ${newOwner.user.tag}'s personal settings to transferred channel ${channelId}`
                );

                if (canManageChannel) {
                  if (refreshedNewOwnerSettings.name) {
                    await updatedChannel.setName(refreshedNewOwnerSettings.name).catch(err => {
                      logger.error(`Failed to set name for transferred channel: ${err.message}`);
                    });
                  }

                  if (typeof refreshedNewOwnerSettings.userLimit !== 'undefined') {
                    await updatedChannel
                      .setUserLimit(refreshedNewOwnerSettings.userLimit)
                      .catch(err => {
                        logger.error(
                          `Failed to set user limit for transferred channel: ${err.message}`
                        );
                      });
                  }

                  if (refreshedNewOwnerSettings.rtcRegion) {
                    await updatedChannel
                      .setRTCRegion(refreshedNewOwnerSettings.rtcRegion)
                      .catch(err => {
                        logger.error(
                          `Failed to set region for transferred channel: ${err.message}`
                        );
                      });
                  }
                } else {
                  logger.warn(
                    `Cannot apply channel settings requiring ManageChannels permission for ${newOwner.user.tag}'s channel`
                  );
                }

                if (updatedBotPermissions.has('ManageRoles')) {
                  if (refreshedNewOwnerSettings.isPrivate) {
                    await updatedChannel.permissionOverwrites
                      .edit(interaction.guild.roles.everyone, {
                        ViewChannel: false,
                      })
                      .catch(err => {
                        logger.error(
                          `Failed to set privacy for transferred channel: ${err.message}`
                        );
                      });
                  }

                  if (refreshedNewOwnerSettings.isLocked) {
                    await updatedChannel.permissionOverwrites
                      .edit(interaction.guild.roles.everyone, {
                        Connect: false,
                      })
                      .catch(err => {
                        logger.error(`Failed to lock transferred channel: ${err.message}`);
                      });
                  }
                } else {
                  logger.warn(
                    `Cannot apply permission settings for ${newOwner.user.tag}'s channel due to missing ManageRoles permission`
                  );
                }

                logger.info(
                  `Successfully applied ${newOwner.user.tag}'s settings to transferred channel ${channelId}`
                );
              } else if (currentOwnerSettings && !newOwnerSettings) {
                client.userSettings.set(targetUserId, {
                  name: currentOwnerSettings.name,
                  userLimit: currentOwnerSettings.userLimit,
                  rtcRegion: currentOwnerSettings.rtcRegion,
                  isPrivate: currentOwnerSettings.isPrivate,
                  isLocked: currentOwnerSettings.isLocked,
                  trustedUsers: [],
                  blockedUsers: [],
                });
                global.saveUserSettings(targetUserId);
                logger.info(
                  `Created new settings for ${newOwner.user.tag} based on the transferred channel`
                );
              } else {
                logger.info(
                  `New owner ${newOwner.user.tag} has no settings, keeping channel as is`
                );
              }

              await interaction
                .editReply({
                  content: `✅ Channel ownership has been transferred to ${newOwner.user.tag} and their settings have been applied.`,
                  flags: MessageFlags.SuppressEmbeds,
                })
                .catch(() => {});
            } catch (applyError) {
              logger.error(`Error applying settings after transfer: ${applyError.message}`);
            }
          }, 5000);
        } catch (error) {
          logger.error(`Error during ownership transfer: ${error.message}`);

          await interaction.editReply({
            content: `${EMOJI[client.user.id].CHECK} Channel ownership has been transferred to ${newOwner.user.tag} in the bot's database, but there was an error updating channel permissions. The channel will function normally but you may need admin help to adjust permissions.`,
            flags: MessageFlags.SuppressEmbeds,
          });
        }
        return;
      }

      if (buttonId.startsWith('purge_button_')) {
        await channelWelcomeHandler.handlePurgeButton(interaction, client);
        return;
      }

      if (buttonId === 'refresh_ping') {
        logger.debug(
          `${buttonSource}: Refresh ping button clicked by ${interaction.user.tag} - handled by collector`
        );
        return;
      }

      if (buttonId.startsWith('cancel_')) {
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CHECK} Action cancelled.`,
          components: [],
        });
        return;
      }

      if (buttonId.startsWith('delete_confirm_')) {
        const confirmSource = `${source}:button:${buttonId}`;
        const channelId = buttonId.split('_')[2];

        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Channel not found.`,
            components: [],
          });
          return;
        }

        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${confirmSource}: User ${interaction.user.tag} attempted delete confirmation without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} You are no longer the owner of this channel.`,
            components: [],
          });
          return;
        }

        const cooldownRemaining = checkCooldown(interaction.user.id, 'delete', targetChannel.id);
        if (cooldownRemaining > 0) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `⏳ Please wait ${cooldownRemaining}s before deleting.`,
            components: [],
          });
          return;
        }

        try {
          logger.info(
            `User ${interaction.user.tag} confirmed deletion for channel ${targetChannel.name} (${channelId})`
          );

          await targetChannel.delete(`Deleted by owner ${interaction.user.tag}`);
          client.tempChannels.delete(channelId);
          logger.info(`Channel ${channelId} deleted successfully.`);
          interaction.deferUpdate().then(() =>
            interaction.editReply({
              content: `${EMOJI[client.user.id].CHECK} Channel has been deleted.`,
              components: [],
            })
          );
        } catch (deleteError) {
          logger.error(
            `${confirmSource}: Failed to delete channel ${channelId}: ${deleteError.message}`
          );
          await handleInteractionError(confirmSource, deleteError, interaction, client);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Failed to delete the channel. The bot might lack permissions or the channel was already deleted.`,
            components: [],
          });
        }
        return;
      }

      if (buttonId.startsWith('join_')) {
        if (!interaction.deferred) {
          const deferred = await deferReplyEphemeral(interaction);
          if (!deferred) {
            logger.warn(`${buttonSource}: Interaction expired before deferral for join button.`);
            return;
          }
        }

        const channelId = buttonId.split('_')[1];
        let channel = null;
        try {
          channel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
          if (!channel && !interaction.inGuild()) {
            logger.warn(
              `${buttonSource}: Join button used in DM, target guild/channel resolution needed.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} Cannot join channel from DMs using this button. Please join the server first.`,
            });
            return;
          }
        } catch (fetchError) {
          logger.error(`${buttonSource}: Error fetching channel ${channelId}: ${fetchError}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Error finding the channel.`,
          });
          return;
        }

        if (!channel) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} This channel no longer exists.`,
          });
          return;
        }

        const channelUrl = `https://discord.com/channels/${channel.guild.id}/${channelId}`;
        try {
          const member = await channel.guild.members.fetch(interaction.user.id).catch(() => null);
          if (!member) {
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].CROSS} You need to join the server first. Direct link: ${channelUrl}`,
            });
            return;
          }
          if (!member.voice.channel) {
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].CROSS} Please join a voice channel first. Direct link: ${channelUrl}`,
            });
            return;
          }
          await member.voice.setChannel(channel);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CHECK} Moved to ${channel.name}.`,
          });
        } catch (moveError) {
          logger.error(
            `${buttonSource}: Error moving user ${interaction.user.id} to ${channelId}: ${moveError}`
          );
          await handleInteractionError(`${buttonSource}:moveUser`, moveError, interaction, client);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Could not move you. Try joining manually: ${channelUrl}`,
          });
        }
        return;
      }

      const needsDeferral = !['name', 'limit'].includes(buttonId);
      if (needsDeferral) {
        const deferred = await deferReplyEphemeral(interaction);
        if (!deferred) {
          logger.warn(`${buttonSource}: Interaction expired before deferral.`);
          return;
        }
      }

      try {
        let userChannel = null;

        if (buttonId !== 'claim' && buttonId !== 'refresh_ping') {
          userChannel = await getUserVoiceChannel(interaction.user.id, interaction.guild, client);

          if (!userChannel) {
            const parts = buttonId.split('_');
            if (parts.length >= 3) {
              const channelId = parts[2];

              if (client.tempChannels.has(channelId)) {
                const channel = await interaction.guild?.channels
                  .fetch(channelId)
                  .catch(() => null);
                if (!channel) {
                  client.tempChannels.delete(channelId);

                  try {
                    global.saveTempChannels?.();
                  } catch (saveError) {
                    logger.error(
                      `Failed to save temp channels after deletion: ${saveError.message}`
                    );
                  }

                  logger.info(
                    `${buttonSource}: Removed deleted channel ${channelId} from tracking`
                  );
                }
              }
            }

            logger.debug(
              `${buttonSource}: User ${interaction.user.tag} not in relevant voice channel.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} You need to be in your temporary voice channel (or own one) to use this button.`,
            });
            return;
          }

          const isOwner = client.tempChannels.get(userChannel.id) === interaction.user.id;
          if (!isOwner) {
            logger.warn(
              `${userChannel.id}: User ${interaction.user.tag} interacted with menu for channel ${userChannel.name} without ownership.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
            });
            return;
          }
        }

        const action = buttonId;
        const handler = buttonHandlers[action];

        if (handler && typeof handler.execute === 'function') {
          const settingButtons = ['name', 'limit', 'privacy', 'region', 'waiting'];
          const needsCooldown = settingButtons.includes(action);
          const cooldownRemaining = needsCooldown
            ? checkCooldown(interaction.user.id, action, userChannel?.id)
            : 0;

          if (cooldownRemaining > 0) {
            logger.debug(
              `${buttonSource}: User ${interaction.user.tag} on cooldown for action ${action} (${cooldownRemaining}s)`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `⏳ Please wait ${cooldownRemaining} second(s) before using this action again.`,
            });
            return;
          }

          const ownerActions = [
            'delete',
            'transfer',
            'privacy',
            'name',
            'limit',
            'region',
            'waiting',
          ];

          const nonOwnerActions = ['claim', 'refresh_ping'];

          if (userChannel && ownerActions.includes(action) && !nonOwnerActions.includes(action)) {
            const isOwner = client.tempChannels.get(userChannel.id) === interaction.user.id;
            if (!isOwner) {
              logger.warn(
                `${buttonSource}: User ${interaction.user.tag} attempted owner action ${action} on channel ${userChannel.id} without ownership.`
              );
              await replyOrFollowUpEphemeral(interaction, {
                content: `${EMOJI[client.user.id].UNTRUST} You must be the owner of this channel to perform this action.`,
              });
              return;
            }
          }

          await handler.execute(interaction, client, userChannel);
        } else {
          logger.warn(`${buttonSource}: No handler found for button ID: ${buttonId}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} This button is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(buttonSource, error, interaction, client);
      }
    } else if (interaction.isAnySelectMenu()) {
      const menuId = interaction.customId;
      const menuSource = `${source}:selectMenu:${menuId}`;
      logger.debug(`Select menu interaction: ${menuId} by ${interaction.user.tag}`);

      const deferred = await deferReplyEphemeral(interaction);
      if (!deferred) {
        logger.warn(`${menuSource}: Interaction expired before deferral.`);
        return;
      }

      try {
        const parts = menuId.split('_');
        const action = parts[0];
        const channelId = parts.slice(2).join('_');

        if (!action || !channelId) {
          logger.error(
            `${menuSource}: Could not extract action or channel ID from custom ID: ${menuId}`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Invalid interaction configuration (ID format error).`,
          });
          return;
        }

        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          logger.warn(
            `${menuSource}: Target channel ${channelId} not found or not a voice channel.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The target channel no longer exists or is invalid.`,
          });
          return;
        }

        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${menuSource}: User ${interaction.user.tag} interacted with menu for channel ${channelId} without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
          });
          return;
        }

        const handler = selectMenuHandlers[action];

        if (handler && typeof handler.handleSelectMenu === 'function') {
          await handler.handleSelectMenu(interaction, client, targetChannel);
        } else {
          logger.warn(`${menuSource}: No select menu handler found for action: ${action}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} This select menu action is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(menuSource, error, interaction, client);
      }
    } else if (interaction.isModalSubmit()) {
      const modalId = interaction.customId;
      const modalSource = `${source}:modal:${modalId}`;
      logger.debug(`Modal submitted: ${modalId} by ${interaction.user.tag}`);

      const deferred = await deferReplyEphemeral(interaction);
      if (!deferred) {
        logger.warn(`${modalSource}: Interaction expired before deferral.`);
        return;
      }

      try {
        const parts = modalId.split('_');
        const action = parts[0];
        const channelId = parts.slice(2).join('_');

        if (!action || !channelId) {
          logger.error(
            `${modalSource}: Could not extract action or channel ID from custom ID: ${modalId}`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Invalid interaction configuration (ID format error).`,
          });
          return;
        }

        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(error => {
          logger.error(
            `${modalSource}: Could not fetch channel ${channelId} for modal submission: ${error.message}`
          );
          return null;
        });

        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          if (client.tempChannels.has(channelId)) {
            client.tempChannels.delete(channelId);

            try {
              global.saveTempChannels?.();
            } catch (saveError) {
              logger.error(`Failed to save temp channels after deletion: ${saveError.message}`);
            }
          }

          logger.warn(
            `${modalSource}: Target channel ${channelId} not found or not a voice channel.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The target channel no longer exists or is invalid.`,
          });
          return;
        }

        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${modalSource}: User ${interaction.user.tag} submitted modal for channel ${channelId} without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
          });
          return;
        }

        if (modalId.startsWith('purge_modal_')) {
          await channelWelcomeHandler.handlePurgeModalSubmit(interaction, client);
          return;
        }

        const handler = modalSubmitHandlers[action];

        if (handler && typeof handler.handleModalSubmit === 'function') {
          await handler.handleModalSubmit(interaction, client, targetChannel);
        } else {
          logger.warn(`${modalSource}: No modal submit handler found for action: ${action}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} This modal action is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(modalSource, error, interaction, client);
      }
    }
  } catch (error) {
    await handleInteractionError(`${source}:toplevel`, error, interaction, client);
  }
};
