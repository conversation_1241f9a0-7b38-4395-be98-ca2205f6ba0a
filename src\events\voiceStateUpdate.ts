/**
 * VoiceStateUpdate Event Handler
 *
 * Handles the creation of temporary voice channels when users join the Join to Create channel.
 * Also handles saving user settings when they leave their temporary channel.
 * Optimized for handling 100+ concurrent users.
 */
import { ChannelType, PermissionFlagsBits, VoiceState } from 'discord.js';
import { JOIN_COOLDOWN } from '../constants';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { sendWelcomeMessage } from './channelWelcome';

interface DiscordError extends Error {
  code?: number;
  guild?: any;
  guildId?: string;
  channelId?: string;
  userId?: string;
}

const channelCreateQueue = [];
const channelDeleteQueue = [];
let isProcessingChannelQueue = false;

const userCooldowns = new Map();
const pendingMoves = new Map();
const channelCreationsInProgress = new Set();

const scheduledDeletions = new Set();

const activeCreations = new Set();

/**
 * Schedule a channel for deletion after it's empty
 * @param {VoiceChannel} channel - The voice channel
 * @param {Client} client - The Discord client
 */
async function scheduleChannelDeletion(channel, client) {
  try {
    const ownerId = client.tempChannels.get(channel.id);
    if (!ownerId) return;

    if (scheduledDeletions.has(channel.id)) return;

    const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

    if (isWaitingRoom) {
      logger.debug(
        `Waiting room ${channel.name} (${channel.id}) is empty, tracking for 1-minute deletion`
      );

      global.emptyWaitingRooms.set(channel.id, Date.now());
      return;
    }

    logger.debug(
      `Channel ${channel.name} (${channel.id}) is empty, scheduling for immediate deletion`
    );
    scheduledDeletions.add(channel.id);

    await new Promise(resolve => setTimeout(resolve, 50));

    const freshChannel = await channel.guild.channels.fetch(channel.id).catch(() => null);
    if (!freshChannel) {
      scheduledDeletions.delete(channel.id);
      client.tempChannels.delete(channel.id);
      dataManager.saveTempChannels(client.tempChannels);
      userCooldowns.delete(ownerId);
      return;
    }

    if (freshChannel.members.size > 0) {
      scheduledDeletions.delete(freshChannel.id);
      return;
    }

    scheduledDeletions.delete(channel.id);
    client.tempChannels.delete(channel.id);
    userCooldowns.delete(ownerId);

    dataManager.saveTempChannels(client.tempChannels).catch(err => {
      logger.warn(`Failed to save temp channels during deletion: ${err.message}`);
    });

    try {
      await freshChannel.delete(`Automatic cleanup: Channel empty`);
      logger.info(`Successfully deleted empty channel ${channel.name} (${channel.id})`);
    } catch (error) {
      logger.warn(`Direct deletion failed, adding to queue: ${error.message}`);
      channelDeleteQueue.push({
        channel: freshChannel,
        timestamp: Date.now(),
      });

      if (!isProcessingChannelQueue) {
        processChannelQueue();
      }
    }
  } catch (error) {
    logger.error(`Error in scheduleChannelDeletion: ${error.message}`);
    scheduledDeletions.delete(channel?.id);
  }
}

/**
 * Handle a user leaving a temporary channel
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 */
async function handleUserLeavingChannel(state, client) {
  if (!state || !state.channel) {
    return;
  }

  const channel = state.channel;
  const member = state.member;
  const userId = member.id;
  const channelId = channel.id;

  try {
    if (client.tempChannels.has(channelId) && client.tempChannels.get(channelId) === userId) {
      logger.debug(
        `User ${member.user.username} is the owner of channel ${channelId}, saving basic settings only`
      );

      let userSettings = client.userSettings.get(userId) || {};

      userSettings.name = channel.name;
      userSettings.userLimit = channel.userLimit;
      userSettings.bitrate = channel.bitrate;
      userSettings.rtcRegion = channel.rtcRegion;

      const everyoneRole = channel.guild.roles.everyone;
      const everyonePerms = channel.permissionOverwrites.cache.get(everyoneRole.id);

      if (everyonePerms) {
        if (everyonePerms.deny.has(PermissionFlagsBits.ViewChannel)) {
          userSettings.isPrivate = true;
        } else {
          userSettings.isPrivate = false;
        }

        if (everyonePerms.deny.has(PermissionFlagsBits.Connect)) {
          userSettings.hasWaitingRoom = true;
        } else {
          userSettings.hasWaitingRoom = false;
        }
      }

      userSettings.lastModified = Date.now();

      client.userSettings.set(userId, userSettings);

      try {
        await dataManager.saveUserSettings({ [userId]: userSettings });
        logger.debug(`Successfully saved basic settings for user ${member.user.username}`);
      } catch (error) {
        logger.warn(
          `Failed to save basic settings for user ${member.user.username}: ${error.message}`
        );

        global.saveUserSettings(userId);
      }

      if (userSettings.hasWaitingRoom && userSettings.waitingRoomId) {
        try {
          logger.debug(
            `Attempting to delete waiting room for user ${member.user.username} (ID: ${userSettings.waitingRoomId})`
          );
          const waitingRoom = await state.guild.channels
            .fetch(userSettings.waitingRoomId)
            .catch(err => {
              logger.warn(
                `Could not fetch waiting room ${userSettings.waitingRoomId}: ${err.message}`
              );
              return null;
            });

          if (waitingRoom) {
            if (waitingRoom.members.size === 0) {
              logger.info(
                `Deleting empty waiting room ${waitingRoom.name} (${waitingRoom.id}) for user ${member.user.username}`
              );
              await waitingRoom
                .delete(`Owner left temp channel and waiting room is empty`)
                .catch(error => {
                  logger.error(`Failed to delete waiting room: ${error.message}`);
                });
              logger.debug(`Waiting room successfully deleted`);
            } else {
              logger.info(
                `Waiting room ${waitingRoom.name} (${waitingRoom.id}) still has users, tracking for 1-minute deletion`
              );
              global.emptyWaitingRooms.set(waitingRoom.id, Date.now());
            }
          } else {
            logger.warn(
              `Waiting room ${userSettings.waitingRoomId} not found, may have been deleted already`
            );
          }

          userSettings.hasWaitingRoom = false;
          userSettings.waitingRoomId = null;
          client.userSettings.set(userId, userSettings);
          global.saveUserSettings(userId);
          logger.debug(
            `User settings updated: waiting room flags cleared for ${member.user.username}`
          );
        } catch (error) {
          logger.error(`Error handling waiting room deletion: ${error.message}`);
        }
      }
    }

    const updatedChannel = await state.guild.channels.fetch(channelId).catch(() => null);
    if (!updatedChannel) {
      logger.debug(`Channel ${channelId} no longer exists, removing from tracking`);
      client.tempChannels.delete(channelId);
      dataManager.saveTempChannels(client.tempChannels);
      return;
    }

    logger.debug(`Fetching latest data for channel ${channelId}`);

    if (updatedChannel.members.size === 0) {
      await scheduleChannelDeletion(updatedChannel, client);
    }
  } catch (error) {
    logger.error(`Error handling user leaving channel: ${error.message}`);
  }
}

export const name = 'voiceStateUpdate';
export const execute = async (oldState, newState, client) => {
  try {
    if (!client.isReady()) return;

    if (newState.member && newState.member.user.bot) return;

    const guildId = newState.guild.id;

    let guildConfig;
    try {
      guildConfig = await dataManager.getGuildSettings(guildId, true);

      if (!guildConfig || !guildConfig.joinChannelId || !guildConfig.tempCategoryId) {
        logger.debug(
          `Skipping voice state update for guild ${guildId} - not configured with MyVC yet.`
        );
        return;
      }
    } catch (err) {
      logger.debug(
        `Error getting guild settings for ${guildId}, skipping voice state update: ${err.message}`
      );
      return;
    }

    const userName = newState.member
      ? newState.member.nickname || newState.member.user.username
      : 'unknown';
    const oldChannelId = oldState?.channelId || 'nowhere';
    const newChannelId = newState?.channelId || 'nowhere';

    logger.debug(
      `Voice state update: User ${userName} moved from ${oldChannelId} to ${newChannelId}`
    );

    let joinChannelId = guildConfig.joinChannelId;
    let tempCategoryId = guildConfig.tempCategoryId;

    try {
      const freshGuildConfig = await dataManager.getGuildSettings(guildId, false);
      if (freshGuildConfig && freshGuildConfig.joinChannelId && freshGuildConfig.tempCategoryId) {
        joinChannelId = freshGuildConfig.joinChannelId;
        tempCategoryId = freshGuildConfig.tempCategoryId;
      }
    } catch (err) {
      logger.debug(`Using cached guild settings for ${guildId}: ${err.message}`);
    }

    logger.debug(
      `Using server-specific settings for guild ${guildId} - joinChannelId: ${joinChannelId}, tempCategoryId: ${tempCategoryId}`
    );

    const guild = newState.guild;

    let joinChannel = guild.channels.cache.get(joinChannelId);
    let category = guild.channels.cache.get(tempCategoryId);

    if (!joinChannel || !category) {
      try {
        if (!joinChannel) {
          joinChannel = await guild.channels.fetch(joinChannelId).catch(async error => {
            if (error.message === 'Unknown Channel') {
              logger.warn(
                `Join channel with ID ${joinChannelId} no longer exists for guild ${guildId}. Cleaning up guild settings.`
              );

              try {
                await dataManager.updateGuildSettings(guildId, {
                  joinChannelId: null,
                });
                logger.info(`Cleaned up invalid join channel ID for guild ${guildId}`);
              } catch (cleanupError) {
                logger.error(`Failed to clean up guild settings for ${guildId}:`, cleanupError);
              }
            }
            return null;
          });

          if (!joinChannel) {
            logger.debug(
              `Join channel with ID ${joinChannelId} not found for guild ${guildId}. Skipping voice state update.`
            );
            return;
          }
        }

        if (!category) {
          category = await guild.channels.fetch(tempCategoryId).catch(async error => {
            if (error.message === 'Unknown Channel') {
              logger.warn(
                `Category with ID ${tempCategoryId} no longer exists for guild ${guildId}. Cleaning up guild settings.`
              );

              try {
                await dataManager.updateGuildSettings(guildId, {
                  tempCategoryId: null,
                  joinChannelId: null,
                });
                logger.info(`Cleaned up invalid category ID for guild ${guildId}`);
              } catch (cleanupError) {
                logger.error(`Failed to clean up guild settings for ${guildId}:`, cleanupError);
              }
            }
            return null;
          });

          if (!category) {
            logger.debug(
              `Category with ID ${tempCategoryId} not found for guild ${guildId}. Skipping voice state update.`
            );
            return;
          }
        }
      } catch (error) {
        logger.debug(`Error fetching channels for guild ${guildId}: ${error.message}`);
        return;
      }
    }

    if (newState.channelId === joinChannelId) {
      const userId = newState.member.id;
      const userName = newState.member.nickname || newState.member.user.username;

      if (activeCreations.has(userId) || channelCreationsInProgress.has(userId)) {
        logger.debug(
          `User ${userName} already has a channel being created, ignoring duplicate request`
        );
        return;
      }

      if (pendingMoves.has(userId)) {
        const pendingMoveData = pendingMoves.get(userId);
        logger.debug(
          `Found pending move for user ${userName} to channel ${pendingMoveData.targetChannelId}, skipping channel creation`
        );
        return;
      }

      const now = Date.now();
      if (userCooldowns.has(userId)) {
        const cooldownEnd = userCooldowns.get(userId);
        if (now < cooldownEnd) {
          const timeRemaining = (cooldownEnd - now) / 1000;
          logger.debug(
            `User ${userName} is on cooldown for ${timeRemaining.toFixed(1)} more seconds`
          );
          return;
        }
      }

      activeCreations.add(userId);
      userCooldowns.set(userId, now + JOIN_COOLDOWN);
      logger.debug(`Added user ${userName} to active creations set and set cooldown`);

      let existingChannel = null;
      for (const [channelId, ownerId] of client.tempChannels.entries()) {
        if (ownerId === userId) {
          existingChannel = await guild.channels.fetch(channelId).catch(() => null);
          if (existingChannel) {
            logger.debug(`Found existing channel ${channelId} for ${userName}, moving user`);
            try {
              await newState.member.voice.setChannel(existingChannel);
              activeCreations.delete(userId);
              return;
            } catch (error) {
              logger.error(`Failed to move user to existing channel: ${error.message}`);

              client.tempChannels.delete(channelId);
            }
          } else {
            client.tempChannels.delete(channelId);
          }
        }
      }

      logger.debug(`Creating new channel for ${userName}`);
      channelCreationsInProgress.add(userId);

      try {
        await handleUserJoiningChannel(newState, client, tempCategoryId);
      } catch (error) {
        logger.error(`Error handling user joining channel: ${error.message}`);
        channelCreationsInProgress.delete(userId);
        activeCreations.delete(userId);
      }
    } else if (oldState.channelId && oldState.channelId !== joinChannelId) {
      if (client.tempChannels.has(oldState.channelId)) {
        const channelOwnerId = client.tempChannels.get(oldState.channelId);
        const userId = oldState.member.id;

        logger.debug(
          `User ${userName} left channel ${oldState.channelId}. Channel owner ID: ${channelOwnerId}, User ID: ${userId}`
        );

        if (newState.channelId && pendingMoves.has(userId)) {
          const pendingMove = pendingMoves.get(userId);
          logger.debug(
            `Detected voice state update for pending move. User: ${userName}, Target: ${pendingMove.targetChannelId}, Current: ${newState.channelId}`
          );

          if (
            pendingMove.targetChannelId === newState.channelId ||
            (newState.channel && newState.channel.parentId === tempCategoryId)
          ) {
            logger.debug(
              `User ${userName} was moved automatically to channel ${newState.channelId}, not processing further`
            );
            return;
          }
        }

        try {
          await handleUserLeavingChannel(oldState, client);
        } catch (error) {
          logger.error(`Error handling user leaving channel: ${error.message}`);
        }
      }
    }
  } catch (error) {
    handleError('voiceStateUpdate', error);
  }
};

/**
 * Queue a channel creation request
 * @param {VoiceState} state - The voice state
 * @param {Client} client - The Discord client
 * @param {string} categoryId - Category ID for the new channel
 */
function queueChannelCreation(state, client, categoryId) {
  channelCreateQueue.push({
    state,
    client,
    categoryId,
    timestamp: Date.now(),
  });

  if (!isProcessingChannelQueue) {
    logger.debug(`Added channel creation to queue (size now: ${channelCreateQueue.length})`);
    processChannelQueue();
  } else {
    logger.debug(
      `Added channel creation to queue while processing (size now: ${channelCreateQueue.length})`
    );
  }
}

/**
 * Process the channel creation and deletion queue
 * This helps prevent hitting Discord API rate limits
 */
async function processChannelQueue() {
  if (isProcessingChannelQueue) return;
  isProcessingChannelQueue = true;

  try {
    logger.debug(
      `Starting to process channel queue. Create: ${channelCreateQueue.length}, Delete: ${channelDeleteQueue.length}`
    );

    if (channelDeleteQueue.length > 0) {
      logger.debug(`Processing ${channelDeleteQueue.length} channel deletions first`);

      const batchSize = 5;
      while (channelDeleteQueue.length > 0) {
        const batch = channelDeleteQueue.splice(0, batchSize);
        const deletionPromises = batch.map(task => {
          return task.channel
            .delete(`Automatic cleanup: Channel empty`)
            .then(() => {
              logger.info(
                `Successfully deleted empty channel ${task.channel.name} (${task.channel.id})`
              );
              return true;
            })
            .catch(error => {
              logger.error(`Error deleting channel ${task.channel.id}: ${error.message}`);
              return false;
            });
        });

        await Promise.allSettled(deletionPromises);

        if (channelDeleteQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    }

    while (channelCreateQueue.length > 0) {
      const task = channelCreateQueue.shift();
      logger.debug(
        `Processing channel creation for ${task.state.member.user.username} (queue size now: ${channelCreateQueue.length})`
      );

      try {
        await createTemporaryChannel(task.state, task.client, task.categoryId);
      } catch (error) {
        logger.error(`Error creating temporary channel: ${error.message}`);

        if (task.state.member && task.state.member.id) {
          channelCreationsInProgress.delete(task.state.member.id);
        }
      }

      if (channelCreateQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
  } finally {
    isProcessingChannelQueue = false;
  }
}

/**
 * Handle a user joining the Join to Create channel
 * Creates a new temporary channel for them by adding the request to the processing queue
 *
 * This function doesn't directly create the channel, but adds the request to a queue
 * that's processed by createTemporaryChannel to avoid hitting Discord rate limits
 *
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 * @param {string} categoryId - The category ID for the new channel
 */
async function handleUserJoiningChannel(state, client, categoryId) {
  try {
    const member = state.member;
    if (!member) {
      logger.error('Cannot create channel: member is null or undefined');
      return;
    }

    const userName = member.nickname || member.user.username;

    const botMember = state.guild.members.me;
    if (botMember) {
      const botPermissions = botMember.permissions.toArray();
      logger.debug(`Bot permissions in guild: ${botPermissions.join(', ')}`);

      const requiredPermissions = ['ViewChannel', 'ManageChannels', 'Connect'];

      const missingPermissions = requiredPermissions.filter(perm => !botPermissions.includes(perm));
      if (missingPermissions.length > 0) {
        logger.warn(`Bot is missing essential permissions: ${missingPermissions.join(', ')}`);
      }
    }

    if (channelCreateQueue.length === 0) {
      logger.debug(`Fast-tracking channel creation for ${userName} (bypassing queue)`);
      try {
        await createTemporaryChannel(state, client, categoryId);
      } catch (error) {
        if (error.code === 50013) {
          logger.error(`Missing permissions to create channel for ${userName}`);
          logger.debug(`Trying to notify user ${userName} via DM about permission issues`);

          try {
            const guildSettings = dataManager.loadGuildSettings();
            const guildConfig = guildSettings[state.guild.id] || {};
            const joinChannelId = guildConfig.joinChannelId;

            if (joinChannelId) {
              const joinChannel = state.guild.channels.cache.get(joinChannelId);
              if (joinChannel) {
                await member.voice.setChannel(joinChannel).catch(() => {});
              }
            }
          } catch {}
        } else {
          logger.error(`Fast-track channel creation failed with error: ${error.message}`);
        }

        queueChannelCreation(state, client, categoryId);
      }
    } else {
      logger.debug(
        `Queueing channel creation for user ${userName} (${channelCreateQueue.length} in queue)`
      );
      queueChannelCreation(state, client, categoryId);
    }
  } catch (error) {
    handleError('handleUserJoiningChannel', error);

    if (state.member && state.member.id) {
      activeCreations.delete(state.member.id);
      channelCreationsInProgress.delete(state.member.id);
    }
  }
}

/**
 * Enhanced error handler that provides detailed permission diagnostics
 * @param {string} action - The action being performed
 * @param {Error} error - The error object
 * @param {Guild} guild - The guild where the error occurred
 * @param {string} channelId - ID of the channel (if applicable)
 * @param {string} userId - ID of the user (if applicable)
 */
function handlePermissionError(action, error, guild, channelId = null, userId = null) {
  logger.error(`[PERMISSION ERROR] Failed to ${action}: ${error.message}`);

  try {
    const bot = guild.members.me;
    if (!bot) {
      logger.error(`Cannot check permissions: Bot not found in guild ${guild.id}`);
      return;
    }

    const botRoles = bot.roles.cache.map(r => ({
      name: r.name,
      id: r.id,
      position: r.position,
      permissions: r.permissions.toArray().join(','),
    }));

    const guildRoles = guild.roles.cache.map(r => ({
      name: r.name,
      id: r.id,
      position: r.position,
    }));

    guildRoles.sort((a, b) => b.position - a.position);

    let channelPerms = null;
    if (channelId) {
      const channel = guild.channels.cache.get(channelId);
      if (channel) {
        channelPerms = bot.permissionsIn(channel).toArray();
      }
    }

    let userInfo = null;
    if (userId) {
      const member = guild.members.cache.get(userId);
      if (member) {
        userInfo = {
          name: member.user.username,
          id: member.id,
          highestRole: member.roles.highest.name,
        };
      }
    }

    logger.error(`
========== PERMISSION DIAGNOSTIC ==========
Action: ${action}
Error: ${error.message}
Guild: ${guild.name} (${guild.id})
Channel: ${channelId || 'N/A'}
User: ${userId || 'N/A'}

Bot Information:
- Name: ${bot.user.username}
- ID: ${bot.id}
- Highest Role: ${bot.roles.highest.name}
- Guild Permissions: ${bot.permissions.toArray().join(', ')}
${channelPerms ? `- Channel Permissions: ${channelPerms.join(', ')}` : ''}

Role Hierarchy (top 5 roles):
${guildRoles
  .slice(0, 5)
  .map(r => `- ${r.name} (${r.position})`)
  .join('\n')}

Bot Roles:
${botRoles.map(r => `- ${r.name} (${r.position})`).join('\n')}

${
  userInfo
    ? `User Information:
- Name: ${userInfo.name}
- ID: ${userInfo.id}
- Highest Role: ${userInfo.highestRole}`
    : ''
}
===========================================
    `);

    fixPermissionIssue(guild, action, error);
  } catch (diagError) {
    logger.error(`Error in permission diagnostics: ${diagError.message}`);
  }
}

/**
 * Attempt to fix common permission issues automatically
 * @param {Guild} guild - The guild to fix
 * @param {string} action - The action that failed
 * @param {Error} error - The error that occurred
 */
function fixPermissionIssue(guild, action, error) {
  try {
    const bot = guild.members.me;
    if (!bot) return;

    const guildSettings = dataManager.loadGuildSettings();
    const guildConfig = guildSettings[guild.id] || {};

    if (guildConfig.tempCategoryId) {
      const category = guild.channels.cache.get(guildConfig.tempCategoryId);
      if (category) {
        logger.info(`Attempting to fix permissions for category ${category.id}`);

        category.permissionOverwrites
          .edit(bot.id, {
            ViewChannel: true,
            Connect: true,
            ManageChannels: true,
          })
          .catch(e => {
            logger.warn(`Could not fix category permissions: ${e.message}`);
          });
      }
    }

    if (action.includes('createTemporaryChannel') || action.includes('create')) {
      logger.info(`Fixing permissions for channel creation in guild ${guild.id}`);

      if (error.code === 50013) {
        logger.warn(`
=== ADMIN INSTRUCTIONS ===
The bot is missing permissions needed to create voice channels.
Please ensure the bot has these permissions:
1. View Channels
2. Manage Channels
3. Connect
4. Move Members
5. Manage Roles

You can fix this by going to:
Server Settings > Roles > [Bot Role] > Enable these permissions
======================
        `);
      }
    }
  } catch (e) {
    logger.error(`Error in fixPermissionIssue: ${e.message}`);
  }
}

/**
 * Creates a temporary voice channel for a user
 * This is called by the channel queue processor to control API rate limits
 *
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 * @param {string} categoryId - The category ID for the new channel
 * @returns {Promise<Channel|null>} The created channel or null if creation failed
 */
async function createTemporaryChannel(state: VoiceState, client, categoryId) {
  if (!state || !state.member) {
    logger.error('Cannot create channel: state or member is null/undefined');
    return null;
  }

  const member = state.member;
  const guild = state.guild;
  const userId = member.id;
  const userName = member.nickname || member.user.username;

  try {
    logger.debug(`Creating temporary channel for ${userName} (ID: ${userId})`);

    let userSettings;
    try {
      userSettings = await dataManager.getUserSettings(userId);

      if (userSettings) {
        logger.info(`Found settings in MongoDB for user ${userName}`);

        client.userSettings.set(userId, userSettings);
      } else {
        userSettings = client.userSettings.get(userId);
        logger.debug(`No settings in MongoDB, using cached settings for user ${userName}`);
      }
    } catch (error) {
      logger.error(`Error getting user settings from MongoDB: ${error.message}`);
      userSettings = client.userSettings.get(userId);
    }

    if (userSettings) {
      logger.info(`Found saved settings for ${userName}`);
      logger.debug(
        `User settings: name=${userSettings.name || 'default'}, isPrivate=${userSettings.isPrivate || false}, hasWaitingRoom=${userSettings.hasWaitingRoom || false}, trustedUsers=${userSettings.trustedUsers?.length || 0}, blockedUsers=${userSettings.blockedUsers?.length || 0}`
      );
    } else {
      logger.info(`No saved settings found for ${userName}, using defaults`);
    }

    const category = guild.channels.cache.get(categoryId);
    if (!category) {
      logger.error(`Category ${categoryId} not found`);
      return null;
    }

    logger.debug(`Creating channel for ${userName} with saved settings`);

    try {
      const channelName = userSettings?.name || `${userName}'s channel`;
      const channel = await guild.channels.create({
        name: channelName,
        type: ChannelType.GuildVoice,
        userLimit: userSettings?.userLimit || 0,
        bitrate: userSettings?.bitrate || 64000,
        rtcRegion: userSettings?.rtcRegion || null,
        parent: category.id,
      });

      logger.info(`Successfully created channel ${channel.name} (${channel.id}) for ${userName}`);

      client.tempChannels.set(channel.id, userId);

      await member.voice.setChannel(channel).catch(error => {
        logger.warn(`Could not move user to new channel: ${error.message}`);
      });

      setTimeout(async () => {
        await sendWelcomeMessage(channel, userId, client);
      }, 1000);

      dataManager.saveTempChannels(client.tempChannels);

      try {
        await channel.permissionOverwrites.edit(guild.members.me.id, {
          ViewChannel: true,
          Connect: true,
          ManageChannels: true,
          ManageRoles: null,
          MuteMembers: null,
          DeafenMembers: null,
          MoveMembers: null,
        });

        await channel.permissionOverwrites.edit(member.id, {
          ViewChannel: true,
          Connect: true,
          Speak: true,
        });

        if (userSettings?.isPrivate) {
          await channel.permissionOverwrites.edit(guild.roles.everyone.id, {
            ViewChannel: null,
          });
          logger.debug(`Applied privacy setting to channel`);
        }

        if (userSettings?.hasWaitingRoom) {
          await channel.permissionOverwrites.edit(guild.roles.everyone.id, {
            Connect: null,
          });
          logger.debug(`Applied waiting room setting to channel`);
        }

        const guildMemberIds = new Set<string>();
        try {
          const members = await guild.members.fetch({ time: 5000 }).catch(() => null);
          if (members) {
            members.forEach((member: any) => guildMemberIds.add(member.id));
            logger.debug(
              `Fetched ${guildMemberIds.size} members from guild ${guild.id} for permission check`
            );
          }
        } catch (error: any) {
          logger.debug(
            `Could not fetch all guild members: ${error.message}. Will apply permissions directly.`
          );
        }

        if (userSettings?.trustedUsers && userSettings.trustedUsers.length > 0) {
          let appliedCount = 0;

          for (const userId of userSettings.trustedUsers) {
            try {
              await channel.permissionOverwrites.edit(userId, {
                ViewChannel: true,
                Connect: true,
              });
              appliedCount++;

              if (guildMemberIds.size > 0 && !guildMemberIds.has(userId)) {
                logger.debug(
                  `Applied trusted permissions for user ${userId} who is not currently in guild ${guild.id}`
                );
              }
            } catch (error) {
              logger.warn(
                `Failed to apply trusted permission for user ${userId}: ${error.message}`
              );
            }
          }
          logger.debug(
            `Applied ${appliedCount}/${userSettings.trustedUsers.length} trusted users to channel`
          );
        }

        if (userSettings?.blockedUsers && userSettings.blockedUsers.length > 0) {
          let appliedCount = 0;

          for (const userId of userSettings.blockedUsers) {
            try {
              await channel.permissionOverwrites.edit(userId, {
                ViewChannel: false,
                Connect: false,
              });
              appliedCount++;

              if (guildMemberIds.size > 0 && !guildMemberIds.has(userId)) {
                logger.debug(
                  `Applied blocked permissions for user ${userId} who is not currently in guild ${guild.id}`
                );
              }
            } catch (error: any) {
              logger.warn(
                `Failed to apply blocked permission for user ${userId}: ${error.message}`
              );
            }
          }
          logger.debug(
            `Applied ${appliedCount}/${userSettings.blockedUsers.length} blocked users to channel`
          );
        }
      } catch (permError) {
        logger.error(`Error setting permissions: ${permError.message}`);
      }

      channelCreationsInProgress.delete(userId);
      activeCreations.delete(userId);
      logger.debug(`Removed ${userName} from creation tracking after successful channel creation`);

      return channel;
    } catch (error) {
      logger.warn(`Failed to create channel: ${error.message}`);

      try {
        const fallbackChannel = await guild.channels.create({
          name: userSettings?.name || `${userName}'s channel`,
          type: ChannelType.GuildVoice,
          userLimit: userSettings?.userLimit || 0,
          bitrate: userSettings?.bitrate || 64000,
          rtcRegion: userSettings?.rtcRegion || null,
        });

        logger.info(`Created fallback channel ${fallbackChannel.name} (${fallbackChannel.id})`);

        client.tempChannels.set(fallbackChannel.id, userId);

        await member.voice.setChannel(fallbackChannel).catch(moveError => {
          logger.warn(`Could not move user to fallback channel: ${moveError.message}`);
        });

        setTimeout(async () => {
          await sendWelcomeMessage(fallbackChannel, userId, client);
        }, 1000);

        dataManager.saveTempChannels(client.tempChannels);

        try {
          await fallbackChannel.permissionOverwrites
            .edit(guild.members.me.id, {
              ViewChannel: true,
              Connect: true,
              ManageChannels: true,
            })
            .catch(() => {});

          await fallbackChannel.permissionOverwrites
            .edit(member.id, {
              ViewChannel: true,
              Connect: true,
              Speak: true,
            })
            .catch(() => {});

          const fallbackGuildMemberIds = new Set<string>();
          try {
            const members = await guild.members.fetch({ time: 5000 }).catch(() => null);
            if (members) {
              members.forEach((member: any) => fallbackGuildMemberIds.add(member.id));
              logger.debug(
                `Fetched ${fallbackGuildMemberIds.size} members from guild ${guild.id} for fallback channel permission check`
              );
            }
          } catch (error: any) {
            logger.debug(
              `Could not fetch all guild members for fallback channel: ${error.message}. Will apply permissions directly.`
            );
          }

          if (userSettings?.trustedUsers && userSettings.trustedUsers.length > 0) {
            let appliedCount = 0;
            for (const userId of userSettings.trustedUsers) {
              try {
                await fallbackChannel.permissionOverwrites
                  .edit(userId, {
                    ViewChannel: true,
                    Connect: true,
                  })
                  .catch(() => {});
                appliedCount++;

                if (fallbackGuildMemberIds.size > 0 && !fallbackGuildMemberIds.has(userId)) {
                  logger.debug(
                    `Applied trusted permissions for user ${userId} who is not currently in guild ${guild.id} (fallback channel)`
                  );
                }
              } catch (error: any) {
                logger.warn(
                  `Failed to apply trusted permission for user ${userId} in fallback channel: ${error.message}`
                );
              }
            }
            logger.debug(
              `Applied ${appliedCount}/${userSettings.trustedUsers.length} trusted users to fallback channel`
            );
          }

          if (userSettings?.blockedUsers && userSettings.blockedUsers.length > 0) {
            let appliedCount = 0;
            for (const userId of userSettings.blockedUsers) {
              try {
                await fallbackChannel.permissionOverwrites
                  .edit(userId, {
                    ViewChannel: false,
                    Connect: false,
                  })
                  .catch(() => {});
                appliedCount++;

                if (fallbackGuildMemberIds.size > 0 && !fallbackGuildMemberIds.has(userId)) {
                  logger.debug(
                    `Applied blocked permissions for user ${userId} who is not currently in guild ${guild.id} (fallback channel)`
                  );
                }
              } catch (error: any) {
                logger.warn(
                  `Failed to apply blocked permission for user ${userId} in fallback channel: ${error.message}`
                );
              }
            }
            logger.debug(
              `Applied ${appliedCount}/${userSettings.blockedUsers.length} blocked users to fallback channel`
            );
          }
        } catch (err) {
          logger.error(`Error setting fallback permissions: ${err.message}`);
        }

        return fallbackChannel;
      } catch (fallbackError) {
        logger.error(`Fallback channel creation failed: ${fallbackError.message}`);
        throw fallbackError;
      }
    }
  } catch (error) {
    logger.error(`Error in createTemporaryChannel: ${error.message}`);
    channelCreationsInProgress.delete(userId);
    activeCreations.delete(userId);
    return null;
  }
}

/**
 * Periodically clean up cooldowns and pending moves
 * This prevents memory leaks from users who disconnect during channel creation
 */
setInterval(() => {
  try {
    const now = Date.now();
    let removedCooldowns = 0;
    let removedPendingMoves = 0;
    let removedCreations = 0;

    for (const [userId, cooldownEnd] of userCooldowns.entries()) {
      if (now > cooldownEnd) {
        userCooldowns.delete(userId);
        removedCooldowns++;
      }
    }

    for (const [userId, moveData] of pendingMoves.entries()) {
      if (now - moveData.timestamp > 3000) {
        pendingMoves.delete(userId);
        removedPendingMoves++;
      }
    }

    for (const userId of channelCreationsInProgress) {
      channelCreationsInProgress.delete(userId);
      activeCreations.delete(userId);
      removedCreations++;
    }

    if (removedCooldowns > 0 || removedPendingMoves > 0 || removedCreations > 0) {
      logger.debug(
        `Cleanup: Removed ${removedCooldowns} cooldowns, ${removedPendingMoves} pending moves, ${removedCreations} stuck creations`
      );
    }
  } catch (error) {
    logger.error(`Error in cleanup interval: ${error.message}`);
  }
}, 2500);

/**
 * Check for empty channels in MyVC Category every 20 seconds and delete them immediately
 * This helps clean up orphaned channels when users switch between guilds
 */
setInterval(async () => {
  try {
    if (!global.client || !global.client.isReady()) return;

    const client = global.client;
    let channelsDeleted = 0;

    const guildSettings = await dataManager.loadGuildSettings();

    for (const guild of client.guilds.cache.values()) {
      try {
        const guildConfig = guildSettings[guild.id];
        if (!guildConfig || !guildConfig.tempCategoryId) continue;

        const categoryId = guildConfig.tempCategoryId;
        const joinChannelId = guildConfig.joinChannelId;

        const category = await guild.channels.fetch(categoryId).catch(() => null);
        if (!category) continue;

        for (const [channelId, channel] of guild.channels.cache.entries()) {
          if (channel.parentId !== categoryId) continue;

          if (channelId === joinChannelId) continue;

          if (channel.name.toLowerCase().includes('join')) continue;

          if (channel.type === ChannelType.GuildVoice && channel.members.size === 0) {
            logger.info(
              `Category cleanup: Found empty channel ${channel.name} (${channelId}), deleting immediately`
            );

            client.tempChannels.delete(channelId);

            try {
              await channel.delete(`Automatic category cleanup: Channel empty`);
              channelsDeleted++;
              logger.info(`Category cleanup: Successfully deleted empty channel ${channelId}`);
            } catch (error) {
              logger.error(
                `Category cleanup: Failed to delete channel ${channelId}: ${error.message}`
              );
            }
          }
        }
      } catch (error) {
        logger.error(`Error cleaning up guild ${guild.id}: ${error.message}`);
      }
    }

    if (channelsDeleted > 0) {
      logger.info(`Category cleanup complete: Deleted ${channelsDeleted} empty channels`);

      dataManager.saveTempChannels(client.tempChannels).catch(err => {
        logger.error(`Failed to save temp channels during category cleanup: ${err.message}`);
      });
    }
  } catch (error) {
    logger.error(`Error in category cleanup interval: ${error.message}`);
  }
}, 20000);

/**
 * Enhanced general error handler to properly direct errors
 */
function handleError(context: string, error: DiscordError): void {
  try {
    logger.error(`Error in ${context}: ${error.message}`);

    if (error.code === 50013) {
      if (error.guild || error.guildId) {
        const guild = error.guild;
        const channelId = error.channelId;
        const userId = error.userId;
        handlePermissionError(context, error, guild, channelId, userId);
        return;
      }
    }

    if (error.stack) {
      logger.debug(
        `Stack trace for ${context} error: ${error.stack.split('\n').slice(0, 3).join('\n')}`
      );
    }
  } catch (handlerError) {
    logger.error(`Error in error handler: ${handlerError.message}`);
  }
}
