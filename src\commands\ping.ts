import { Client, EmbedBuilder, <PERSON>lashCommandBuilder, ChatInputCommandInteraction } from 'discord.js';
import logger from '../utils/logger';

export const data = new SlashCommandBuilder()
  .setName('ping')
  .setDescription('Advanced ping command for the nerds out there.');

function getPingBar(ping: number): string {
  if (ping < 60) return '🟩🟩🟩🟩🟩 Excellent';
  if (ping < 120) return '🟩🟩🟩🟨🟨 Good';
  if (ping < 200) return '🟨🟨🟧🟧🟥 Average';
  return '🟥🟥🟥🟥🟥 Poor';
}

function getOrdinalWord(n: number): string {
  return n.toString();
}

export const execute = async (interaction: ChatInputCommandInteraction, client: Client) => {
  try {
    await interaction.reply({
      content: "Pinging bot's latency...",
      fetchReply: true,
      ephemeral: true,
    });
    const times: number[] = [];

    for (let i = 0; i < 3; i++) {
      const pingNumber = i + 1;
      const start = Date.now();

      await interaction.editReply({
        content: `Pinging... (attempt ${getOrdinalWord(pingNumber)} of 3...)`,
      });
      const end = Date.now();
      const pingMs = end - start;
      times.push(pingMs);

      await interaction.editReply({
        content: `Pinging... (attempt ${getOrdinalWord(pingNumber)}: ${pingMs}ms)`,
      });
    }

    const avg = times.reduce((a, b) => a + b, 0) / times.length;
    const total = times.reduce((a, b) => a + b, 0);

    const qualityBars = times
      .map((t, i) => `Ping ${getOrdinalWord(i + 1)}: ${t}ms ${getPingBar(t)}`)
      .join('\n');
    const avgBar = getPingBar(avg);

    const embed = new EmbedBuilder()
      .setTitle('🏓 Pong!')
      .setColor(0x00ff00)
      .setDescription(
        `**Individual Pings:**\n${qualityBars}\n\n` +
          `**Average Roundtrip:** ${avg.toFixed(1)}ms ${avgBar}\n` +
          `**Total for Pings:** ${total}ms\n` +
          `**Bot WebSocket Latency:** ${client.ws.ping}ms`
      )
      .setAuthor({
        name: `Requested by ${interaction.user.tag}`,
        iconURL: interaction.user.displayAvatarURL(),
      })
      .setFooter({ text: `Ping command | Bot: ${client.user?.username || 'N/A'}` });

    if (interaction.guild?.iconURL()) {
      embed.setThumbnail(interaction.guild.iconURL());
    }

    await interaction.editReply({ content: '', embeds: [embed] });

    logger.info(
      `Ping command used by ${interaction.user.tag} - Bot WebSocket Latency: ${Math.round(client.ws.ping)}ms, Average Roundtrip: ${avg.toFixed(1)}ms`
    );
  } catch (error) {
    logger.error(`Error in ping command: ${error}`);

    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: 'An error occurred while checking the ping.',
        ephemeral: true,
      });
    } else {
      await interaction.editReply({ content: 'An error occurred while checking the ping.' });
    }
  }
};
